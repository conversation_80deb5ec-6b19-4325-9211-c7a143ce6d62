/**
 * Model Context
 * Voice model management with download, loading, and state tracking
 * Integrates with SetupFlowContext for step lifecycle management
 */

import React, { 
  createContext, 
  useContext, 
  useReducer, 
  useCallback, 
  useEffect,
  useRef
} from 'react';

import { useStepIntegration } from '../hooks/useStepIntegration';
import type { ModelState, VoiceModel, DownloadProgress, LoadingProgress } from '../types/model';

// ============================================================================
// ACTIONS
// ============================================================================

type ModelAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_REFRESHING'; payload: boolean }
  | { type: 'SET_MODELS'; payload: VoiceModel[] }
  | { type: 'SELECT_MODEL'; payload: VoiceModel | null }
  | { type: 'SET_LOADED_MODEL'; payload: string | null }
  | { type: 'SET_DOWNLOAD_PROGRESS'; payload: DownloadProgress | null }
  | { type: 'SET_LOADING_PROGRESS'; payload: LoadingProgress | null }
  | { type: 'UPDATE_MODEL'; payload: { id: string; updates: Partial<VoiceModel> } }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' };

// ============================================================================
// REDUCER
// ============================================================================

function modelReducer(state: ModelState, action: ModelAction): ModelState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_REFRESHING':
      return { ...state, refreshing: action.payload };

    case 'SET_MODELS':
      return { ...state, models: action.payload, loading: false, refreshing: false, error: null };

    case 'SELECT_MODEL':
      return { ...state, selectedModel: action.payload };

    case 'SET_LOADED_MODEL':
      return {
        ...state,
        loadedModel: action.payload,
        models: state.models.map(model => ({
          ...model,
          isLoaded: model.id === action.payload,
        })),
      };

    case 'SET_DOWNLOAD_PROGRESS':
      return { ...state, downloadProgress: action.payload };

    case 'SET_LOADING_PROGRESS':
      return { ...state, loadingProgress: action.payload };

    case 'UPDATE_MODEL':
      return {
        ...state,
        models: state.models.map(model =>
          model.id === action.payload.id
            ? { ...model, ...action.payload.updates }
            : model
        ),
        selectedModel: state.selectedModel?.id === action.payload.id
          ? { ...state.selectedModel, ...action.payload.updates }
          : state.selectedModel,
      };

    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };

    case 'CLEAR_ERROR':
      return { ...state, error: null };

    default:
      return state;
  }
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: ModelState = {
  models: [],
  selectedModel: null,
  loadedModel: null,
  downloadProgress: null,
  loadingProgress: null,
  loading: false,
  refreshing: false,
  error: null,
};

// ============================================================================
// CONTEXT INTERFACE
// ============================================================================

interface ModelContextValue {
  state: ModelState;
  
  // Core actions
  loadModels: () => Promise<void>;
  selectModel: (model: VoiceModel) => void;
  downloadModel: (model: VoiceModel) => Promise<void>;
  loadModel: (model: VoiceModel, userInitiated?: boolean) => Promise<void>;
  cancelDownload: () => Promise<void>;
  
  // Utility actions
  refreshModels: () => Promise<void>;
  clearError: () => void;
  
  // Convenience getters
  canProceedToNext: boolean;
  isModelReady: boolean;
  isUserInitiatedLoading: boolean;
}

const ModelContext = createContext<ModelContextValue | null>(null);

// ============================================================================
// PROVIDER
// ============================================================================

interface ModelProviderProps {
  children: React.ReactNode;
}

export function ModelProvider({ children }: ModelProviderProps) {
  const [state, dispatch] = useReducer(modelReducer, initialState);
  
  // Refs for event handling and state tracking
  const progressUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isDownloadCompleteRef = useRef(false);
  const isLoadingCompleteRef = useRef(false);
  const userInitiatedLoadingRef = useRef(false);

  // Computed state for proceed logic
  const canProceedToNext = Boolean(state.loadedModel);
  const isModelReady = Boolean(state.selectedModel?.isLoaded || state.loadedModel);

  // ============================================================================
  // CORE MODEL MANAGEMENT
  // ============================================================================

  // Internal function for loading models (used by both loadModels and refreshModels)
  const loadModelsInternal = useCallback(async (isRefresh = false) => {
    try {
      console.log(`🔍 ${isRefresh ? 'Refreshing' : 'Loading'} voice models...`);
      if (isRefresh) {
        dispatch({ type: 'SET_REFRESHING', payload: true });
      } else {
        dispatch({ type: 'SET_LOADING', payload: true });
      }
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.sendCommand) {
        throw new Error('Python backend not available');
      }

      // Batch API calls for efficiency
      const [modelsResult, downloadedResult, loadedResult, recommendedResult] = await Promise.all([
        window.electron.sendCommand('models.get_all'),
        window.electron.sendCommand('models.get_downloaded'),
        window.electron.sendCommand('models.get_loaded'),
        window.electron.sendCommand('models.get_recommended')
      ]);

      if (!modelsResult.success) {
        throw new Error(modelsResult.error || 'Failed to load models');
      }

      // Extract data from results
      const downloadedIds = downloadedResult.success ? downloadedResult.data?.map((m: any) => m.id) || [] : [];
      const currentLoadedModel = loadedResult.success ? loadedResult.data?.id || null : null;
      const recommendedIds = recommendedResult.success ? recommendedResult.data?.map((m: any) => m.id) || [] : [];

      console.log('📊 Model data loaded:', {
        totalModels: modelsResult.data?.length || 0,
        downloadedCount: downloadedIds.length,
        currentLoadedModel,
        recommendedCount: recommendedIds.length,
      });

      // Process models data with type safety
      const processedModels: VoiceModel[] = (modelsResult.data || []).map((model: any) => ({
        id: model.id,
        name: model.name,
        size: model.size,
        sizeBytes: model.sizeBytes || 0,
        language: model.languages || 'English',
        type: model.type || 'whisper',
        downloadUrl: model.downloadUrl || '',
        isDownloaded: downloadedIds.includes(model.id) || model.id === currentLoadedModel,
        isLoaded: model.id === currentLoadedModel,
        isRecommended: recommendedIds.includes(model.id),
        description: model.description || '',
        isMultilingual: !model.isEnglishOnly
      }));

      // Sort models: loaded first, then recommended, then by size
      const sortedModels = [...processedModels].sort((a, b) => {
        if (a.isLoaded && !b.isLoaded) return -1;
        if (!a.isLoaded && b.isLoaded) return 1;
        if (a.isRecommended && !b.isRecommended) return -1;
        if (!a.isRecommended && b.isRecommended) return 1;
        return a.sizeBytes - b.sizeBytes;
      });

      dispatch({ type: 'SET_MODELS', payload: sortedModels });
      dispatch({ type: 'SET_LOADED_MODEL', payload: currentLoadedModel });

      // Auto-select appropriate model
      const loaded = sortedModels.find(m => m.isLoaded);
      const recommended = sortedModels.find(m => m.isRecommended);
      const firstAvailable = sortedModels[0];

      const modelToSelect = loaded || recommended || firstAvailable;
      if (modelToSelect) {
        console.log('🎯 Auto-selecting model:', modelToSelect.id);
        dispatch({ type: 'SELECT_MODEL', payload: modelToSelect });
      }

      console.log('✓ Models loaded successfully');
    } catch (error: any) {
      console.error('❌ Failed to load models:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    } finally {
      if (isRefresh) {
        dispatch({ type: 'SET_REFRESHING', payload: false });
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
  }, []);

  const loadModels = useCallback(async () => {
    await loadModelsInternal(false);
  }, [loadModelsInternal]);



  const selectModel = useCallback((model: VoiceModel) => {
    console.log('🎯 Selecting model:', model.id);
    dispatch({ type: 'SELECT_MODEL', payload: model });
  }, []);

  const downloadModel = useCallback(async (model: VoiceModel) => {
    try {
      console.log('⬇️ Starting download for model:', model.id);
      dispatch({ type: 'CLEAR_ERROR' });
      dispatch({ type: 'SET_DOWNLOAD_PROGRESS', payload: {
        modelId: model.id,
        progress: 0,
        status: 'downloading'
      }});

      if (!window.electron?.sendCommand) {
        throw new Error('Python backend not available');
      }

      const result = await window.electron.sendCommand('models.download', { 
        model_id: model.id 
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to start download');
      }

      console.log('✓ Download started successfully');
    } catch (error: any) {
      console.error('❌ Failed to start download:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
      dispatch({ type: 'SET_DOWNLOAD_PROGRESS', payload: null });
    }
  }, []);

  const loadModel = useCallback(async (model: VoiceModel, userInitiated = true) => {
    try {
      console.log('🚀 Loading model:', model.id, '(user initiated:', userInitiated, ')');
      dispatch({ type: 'CLEAR_ERROR' });
      
      // Check if already loaded
      if (model.isLoaded || state.loadedModel === model.id) {
        console.log(`Model ${model.id} is already loaded`);
        return;
      }
      
      // Set flag for user-initiated loading
      userInitiatedLoadingRef.current = userInitiated;
      
      dispatch({ type: 'SET_LOADING_PROGRESS', payload: {
        modelId: model.id,
        status: 'loading',
        stage: 'Initializing...'
      }});

      if (!window.electron?.sendCommand) {
        throw new Error('Python backend not available');
      }

      const result = await window.electron.sendCommand('models.load', { 
        model_id: model.id 
      });

      if (!result.success) {
        userInitiatedLoadingRef.current = false;
        throw new Error(result.error || 'Failed to load model');
      }

      // Set as active model
      await window.electron.sendCommand('models.set_active', { 
        model_id: model.id 
      });

      console.log('✓ Model load request sent successfully');
    } catch (error: any) {
      console.error('❌ Failed to load model:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
      dispatch({ type: 'SET_LOADING_PROGRESS', payload: null });
      userInitiatedLoadingRef.current = false;
    }
  }, [state.loadedModel]);

  const cancelDownload = useCallback(async () => {
    if (state.downloadProgress) {
      try {
        console.log('⏹️ Canceling download for model:', state.downloadProgress.modelId);
        dispatch({ type: 'CLEAR_ERROR' });
        
        const result = await window.electron.sendCommand('models.download.cancel', { 
          model_id: state.downloadProgress.modelId 
        });

        if (!result.success) {
          throw new Error(result.error || 'Failed to cancel download');
        }

        dispatch({ type: 'SET_DOWNLOAD_PROGRESS', payload: null });
        console.log('✓ Download canceled successfully');
        
      } catch (error: any) {
        console.error('❌ Error canceling download:', error);
        dispatch({ type: 'SET_ERROR', payload: error.message });
      }
    }
  }, [state.downloadProgress]);

  const refreshModels = useCallback(async () => {
    console.log('🔄 Refreshing models without loading state...');

    try {
      dispatch({ type: 'SET_REFRESHING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.sendCommand) {
        throw new Error('Python backend not available');
      }

      // Batch API calls for better performance
      const [modelsResult, downloadedResult, loadedResult, recommendedResult] = await Promise.all([
        window.electron.sendCommand('models.get_all'),
        window.electron.sendCommand('models.get_downloaded'),
        window.electron.sendCommand('models.get_loaded'),
        window.electron.sendCommand('models.get_recommended')
      ]);

      if (!modelsResult.success) {
        throw new Error(modelsResult.error || 'Failed to load models');
      }

      const downloadedIds = downloadedResult.success ? downloadedResult.data?.map((m: any) => m.id) || [] : [];
      const currentLoadedModel = loadedResult.success ? loadedResult.data?.id || null : null;
      const recommendedIds = recommendedResult.success ? recommendedResult.data?.map((m: any) => m.id) || [] : [];

      // Update loaded model state
      dispatch({ type: 'SET_LOADED_MODEL', payload: currentLoadedModel });

      // Process models data
      const processedModels: VoiceModel[] = (modelsResult.data || []).map((model: any) => ({
        id: model.id,
        name: model.name,
        size: model.size,
        sizeBytes: model.sizeBytes || 0,
        language: model.languages || 'English',
        type: model.type || 'whisper',
        downloadUrl: model.downloadUrl || '',
        isDownloaded: downloadedIds.includes(model.id) || model.id === currentLoadedModel,
        isLoaded: model.id === currentLoadedModel,
        isRecommended: recommendedIds.includes(model.id),
        description: model.description || '',
        isMultilingual: !model.isEnglishOnly
      }));

      // Sort models: loaded first, then recommended, then by size
      const sortedModels = processedModels.sort((a, b) => {
        if (a.isLoaded && !b.isLoaded) return -1;
        if (!a.isLoaded && b.isLoaded) return 1;
        if (a.isRecommended && !b.isRecommended) return -1;
        if (!a.isRecommended && b.isRecommended) return 1;
        return a.sizeBytes - b.sizeBytes;
      });

      dispatch({ type: 'SET_MODELS', payload: sortedModels });

      // Auto-select appropriate model
      const currentState = state;
      const loaded = sortedModels.find(m => m.isLoaded);
      const currentlySelected = sortedModels.find(m => m.id === currentState.selectedModel?.id);
      
      if (loaded) {
        dispatch({ type: 'SELECT_MODEL', payload: loaded });
      } else if (currentlySelected) {
        dispatch({ type: 'SELECT_MODEL', payload: currentlySelected });
      } else {
        const recommended = sortedModels.find(m => m.isRecommended);
        const downloaded = sortedModels.find(m => m.isDownloaded);
        
        if (recommended) {
          dispatch({ type: 'SELECT_MODEL', payload: recommended });
        } else if (downloaded) {
          dispatch({ type: 'SELECT_MODEL', payload: downloaded });
        } else if (sortedModels.length > 0) {
          dispatch({ type: 'SELECT_MODEL', payload: sortedModels[0] });
        }
      }

      console.log('✓ Models refreshed without loading overlay');
    } catch (error: any) {
      console.error('❌ Error refreshing models:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    } finally {
      dispatch({ type: 'SET_REFRESHING', payload: false });
    }
  }, [state]);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // ============================================================================
  // PYTHON EVENT HANDLERS
  // ============================================================================

  const updateDownloadProgress = useCallback((progressData: any) => {
    const { model_id, progress, status, bytes_downloaded, total_bytes, speed } = progressData;
    
    // Clear any pending updates
    if (progressUpdateTimeoutRef.current) {
      clearTimeout(progressUpdateTimeoutRef.current);
    }
    
    // Update immediately for critical status changes
    if (status === 'completed' || status === 'failed' || status === 'cancelled') {
      dispatch({ type: 'SET_DOWNLOAD_PROGRESS', payload: {
        modelId: model_id,
        progress: Math.round(progress),
        status,
        bytesDownloaded: bytes_downloaded,
        totalBytes: total_bytes,
        speed
      }});
      
      if (status === 'completed' && !isDownloadCompleteRef.current) {
        isDownloadCompleteRef.current = true;
        console.log('🎉 Download completed for model:', model_id);
        
        // Update model status optimistically
        dispatch({ type: 'UPDATE_MODEL', payload: {
          id: model_id,
          updates: { isDownloaded: true }
        }});
        
        // Refresh models and clear progress after completion
        setTimeout(() => {
          loadModels();
          dispatch({ type: 'SET_DOWNLOAD_PROGRESS', payload: null });
          isDownloadCompleteRef.current = false;
        }, 1500);
      }
    } else {
      // Debounce progress updates to prevent UI thrashing
      progressUpdateTimeoutRef.current = setTimeout(() => {
        dispatch({ type: 'SET_DOWNLOAD_PROGRESS', payload: {
          modelId: model_id,
          progress: Math.round(progress),
          status,
          bytesDownloaded: bytes_downloaded,
          totalBytes: total_bytes,
          speed
        }});
      }, 100);
    }
  }, [loadModels]);

  const updateLoadingProgress = useCallback((progressData: any) => {
    const { model_id, status, stage, progress } = progressData;
    
    dispatch({ type: 'SET_LOADING_PROGRESS', payload: {
      modelId: model_id,
      status,
      stage,
      progress: progress ? Math.round(progress) : undefined
    }});

    if ((status === 'completed' || status === 'loaded') && !isLoadingCompleteRef.current) {
      isLoadingCompleteRef.current = true;
      console.log('🎉 Model loaded successfully:', model_id);
      
      // Update loaded model state
      dispatch({ type: 'SET_LOADED_MODEL', payload: model_id });
      
      // Update model status optimistically
      dispatch({ type: 'UPDATE_MODEL', payload: {
        id: model_id,
        updates: { isLoaded: true, isDownloaded: true }
      }});
      
      // Clear loading progress after delay
      setTimeout(() => {
        dispatch({ type: 'SET_LOADING_PROGRESS', payload: null });
        isLoadingCompleteRef.current = false;
        userInitiatedLoadingRef.current = false;
      }, 2000);
    } else if (status === 'failed') {
      console.error('❌ Model loading failed:', model_id);
      userInitiatedLoadingRef.current = false;
      setTimeout(() => {
        dispatch({ type: 'SET_LOADING_PROGRESS', payload: null });
      }, 3000);
    }
  }, []);

  // ============================================================================
  // PYTHON MESSAGE LISTENER
  // ============================================================================

  useEffect(() => {
    console.log('🔧 Setting up model event listeners');

    if (typeof window !== 'undefined' && window.electron?.onBackendMessage) {
      const cleanup = window.electron.onBackendMessage((message: any) => {
        // Filter out high-frequency audio events to prevent console spam
        if (message.type !== 'event' || message.event !== 'audio_levels') {
          console.log('📨 Python message:', message);
        }
        
        if (message.type === 'event') {
          switch (message.event) {
            case 'model_download_progress':
              updateDownloadProgress(message.data);
              break;
            case 'model_loading_progress':
              updateLoadingProgress(message.data);
              break;
            case 'model_loaded':
              updateLoadingProgress({
                model_id: message.data.model_id,
                status: 'completed'
              });
              break;
            case 'model_loading_failed':
              updateLoadingProgress({
                model_id: message.data.model_id,
                status: 'failed'
              });
              break;
          }
        }
      });

      return () => {
        console.log('🧹 Cleaning up model event listeners');
        cleanup();
        if (progressUpdateTimeoutRef.current) {
          clearTimeout(progressUpdateTimeoutRef.current);
        }
      };
    }
  }, [updateDownloadProgress, updateLoadingProgress]);

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  // Load models on mount
  useEffect(() => {
    console.log('🚀 Initializing ModelProvider');
    loadModels();
  }, [loadModels]);

  // ============================================================================
  // STEP LIFECYCLE INTEGRATION
  // ============================================================================

  // Use the step integration hook to handle proceed state and lifecycle
  useStepIntegration({
    step: 'model',
    canProceed: canProceedToNext,
    onEnter: async () => {
      console.log('🤖 Entering model step - loading available models');
      await loadModels();
    },
    onExit: async () => {
      console.log('🤖 Exiting model step');
      // Model step doesn't need cleanup
    },
    deps: []
  });

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const contextValue: ModelContextValue = {
    state,
    loadModels,
    selectModel,
    downloadModel,
    loadModel,
    cancelDownload,
    refreshModels,
    clearError,
    canProceedToNext,
    isModelReady,
    isUserInitiatedLoading: userInitiatedLoadingRef.current,
  };

  return (
    <ModelContext.Provider value={contextValue}>
      {children}
    </ModelContext.Provider>
  );
}

// ============================================================================
// HOOK
// ============================================================================

export function useModel(): ModelContextValue {
  const context = useContext(ModelContext);
  if (!context) {
    throw new Error('useModel must be used within a ModelProvider');
  }
  return context;
} 