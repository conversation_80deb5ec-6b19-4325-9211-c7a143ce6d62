/**
 * Audio Device Selector
 * Component for selecting audio input/output devices matching app-latest design exactly
 * Includes default device toggle and detailed device information
 */

import { Label } from '@src/components/ui/label';
import { Switch } from '@src/components/ui/switch';
import { Button } from '@src/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@src/components/ui/select';
import { HiRefresh, HiMicrophone, HiVolumeUp } from 'react-icons/hi';
import { LoadingSpinner } from '../shared/LoadingSpinner';
import type { AudioDevice } from '../../types/audio';

// ============================================================================
// TYPES
// ============================================================================

interface AudioDeviceSelectorProps {
  inputDevices: AudioDevice[];
  outputDevices: AudioDevice[];
  selectedInputDevice: string;
  selectedOutputDevice: string;
  useDefaultInput: boolean;
  useDefaultOutput: boolean;
  onSelectInputDevice: (deviceId: string) => void;
  onSelectOutputDevice: (deviceId: string) => void;
  onSetUseDefaultInput: (useDefault: boolean) => void;
  onSetUseDefaultOutput: (useDefault: boolean) => void;
  onRefresh: () => void;
  loading?: boolean;
  refreshing?: boolean;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function AudioDeviceSelector({
  inputDevices,
  outputDevices,
  selectedInputDevice,
  selectedOutputDevice,
  useDefaultInput,
  useDefaultOutput,
  onSelectInputDevice,
  onSelectOutputDevice,
  onSetUseDefaultInput,
  onSetUseDefaultOutput,
  onRefresh,
  loading = false,
  refreshing = false,
  className
}: AudioDeviceSelectorProps) {
  // Find selected device objects for display
  const selectedInput = inputDevices.find(d => d.id === selectedInputDevice);
  const selectedOutput = outputDevices.find(d => d.id === selectedOutputDevice);

  if (loading) {
    return (
      <div className={`text-center py-8 ${className || ''}`}>
        <HiRefresh className="w-6 h-6 animate-spin mx-auto mb-2 text-primary" />
        <p className="text-sm text-muted-foreground">Loading audio devices...</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Microphone Selection */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <HiMicrophone className="w-4 h-4 text-primary" />
            <label className="text-sm font-medium">Microphone</label>
          </div>
          
          {/* Use Default Switch - Inline */}
          <div className="flex items-center space-x-2">
            <Label htmlFor="use-default-input" className="text-xs text-muted-foreground">
              Use default
            </Label>
            <Switch
              id="use-default-input"
              checked={useDefaultInput}
              onCheckedChange={onSetUseDefaultInput}
            />
          </div>
        </div>
        
        {/* Device Selector (hidden when using default) */}
        {!useDefaultInput && (
          <Select value={selectedInputDevice} onValueChange={onSelectInputDevice}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select microphone..." />
            </SelectTrigger>
            <SelectContent>
              {inputDevices.map((device) => (
                <SelectItem key={device.id} value={device.id}>
                  <span>{device.name}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
        
        {selectedInput && (
          <p className="text-xs text-muted-foreground">
            {useDefaultInput && <span className="text-primary">Default: </span>}
            {selectedInput.channels} ch • {selectedInput.sampleRate} Hz • {selectedInput.hostApi}
          </p>
        )}
      </div>

      {/* Speaker Selection */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <HiVolumeUp className="w-4 h-4 text-primary" />
            <label className="text-sm font-medium">Speakers</label>
          </div>
          
          {/* Use Default Switch - Inline */}
          <div className="flex items-center space-x-2">
            <Label htmlFor="use-default-output" className="text-xs text-muted-foreground">
              Use default
            </Label>
            <Switch
              id="use-default-output"
              checked={useDefaultOutput}
              onCheckedChange={onSetUseDefaultOutput}
            />
          </div>
        </div>
        
        {/* Device Selector (hidden when using default) */}
        {!useDefaultOutput && (
          <Select value={selectedOutputDevice} onValueChange={onSelectOutputDevice}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select speakers..." />
            </SelectTrigger>
            <SelectContent>
              {outputDevices.map((device) => (
                <SelectItem key={device.id} value={device.id}>
                  <span>{device.name}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
        
        {selectedOutput && (
          <p className="text-xs text-muted-foreground">
            {useDefaultOutput && <span className="text-primary">Default: </span>}
            {selectedOutput.channels} ch • {selectedOutput.sampleRate} Hz • {selectedOutput.hostApi}
          </p>
        )}
      </div>

      {/* Refresh Button */}
      <Button
        onClick={onRefresh}
        variant="outline"
        size="sm"
        className="w-full"
        disabled={loading || refreshing}
      >
        <HiRefresh className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
        Refresh Devices
      </Button>
    </div>
  );
}

export default AudioDeviceSelector; 