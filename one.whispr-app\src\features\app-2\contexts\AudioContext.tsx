/**
 * Audio Context
 * Audio device management with monitoring and settings persistence
 * Integrates with SetupFlowContext for step lifecycle management
 */

import React, { 
  createContext, 
  useContext, 
  useReducer, 
  useCallback, 
  useEffect,
  useRef
} from 'react';

import { useStepIntegration } from '../hooks/useStepIntegration';
import type { AudioState, AudioDevice, AudioLevel } from '../types/audio';
import type { DeviceType } from '../types/core';

// ============================================================================
// ACTIONS
// ============================================================================

type AudioAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_REFRESHING'; payload: boolean }
  | { type: 'SET_DEVICES'; payload: { input: AudioDevice[]; output: AudioDevice[] } }
  | { type: 'SELECT_INPUT_DEVICE'; payload: string }
  | { type: 'SELECT_OUTPUT_DEVICE'; payload: string }
  | { type: 'SET_USE_DEFAULT_INPUT'; payload: boolean }
  | { type: 'SET_USE_DEFAULT_OUTPUT'; payload: boolean }
  | { type: 'SET_AUDIO_LEVEL'; payload: AudioLevel }
  | { type: 'SET_MONITORING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' }
  | { type: 'BATCH_UPDATE_USE_DEFAULT_INPUT'; payload: { useDefault: boolean; selectedDevice: string } }
  | { type: 'BATCH_UPDATE_USE_DEFAULT_OUTPUT'; payload: { useDefault: boolean; selectedDevice: string } };

// ============================================================================
// REDUCER
// ============================================================================

function audioReducer(state: AudioState, action: AudioAction): AudioState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_REFRESHING':
      return { ...state, refreshing: action.payload };

    case 'SET_DEVICES':
      return {
        ...state,
        inputDevices: action.payload.input,
        outputDevices: action.payload.output,
        loading: false,
        refreshing: false,
        error: null,
      };

    case 'SELECT_INPUT_DEVICE':
      return { ...state, selectedInputDevice: action.payload };

    case 'SELECT_OUTPUT_DEVICE':
      return { ...state, selectedOutputDevice: action.payload };

    case 'SET_USE_DEFAULT_INPUT':
      return { ...state, useDefaultInputDevice: action.payload };

    case 'SET_USE_DEFAULT_OUTPUT':
      return { ...state, useDefaultOutputDevice: action.payload };

    case 'SET_AUDIO_LEVEL':
      return { ...state, audioLevel: action.payload };

    case 'SET_MONITORING':
      return { ...state, isMonitoring: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };

    case 'CLEAR_ERROR':
      return { ...state, error: null };

    case 'BATCH_UPDATE_USE_DEFAULT_INPUT':
      return { 
        ...state, 
        useDefaultInputDevice: action.payload.useDefault,
        selectedInputDevice: action.payload.selectedDevice
      };

    case 'BATCH_UPDATE_USE_DEFAULT_OUTPUT':
      return { 
        ...state, 
        useDefaultOutputDevice: action.payload.useDefault,
        selectedOutputDevice: action.payload.selectedDevice
      };

    default:
      return state;
  }
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: AudioState = {
  inputDevices: [],
  outputDevices: [],
  selectedInputDevice: '',
  selectedOutputDevice: '',
  useDefaultInputDevice: true,
  useDefaultOutputDevice: true,
  audioLevel: { microphone: 0 },
  isMonitoring: false,
  loading: false,
  refreshing: false,
  error: null,
};

// ============================================================================
// CONTEXT INTERFACE
// ============================================================================

interface AudioContextValue {
  state: AudioState;
  
  // Core actions
  loadDevices: () => Promise<void>;
  selectInputDevice: (deviceId: string) => void;
  selectOutputDevice: (deviceId: string) => void;
  setUseDefaultInput: (useDefault: boolean) => void;
  setUseDefaultOutput: (useDefault: boolean) => void;
  
  // Monitoring actions
  startMonitoring: () => Promise<void>;
  stopMonitoring: () => Promise<void>;
  
  // Utility actions
  refreshDevices: () => Promise<void>;
  clearError: () => void;
  
  // Convenience getters
  canProceedToNext: boolean;
  isAudioReady: boolean;
}

const AudioContext = createContext<AudioContextValue | null>(null);

// ============================================================================
// PROVIDER
// ============================================================================

interface AudioProviderProps {
  children: React.ReactNode;
}

export function AudioProvider({ children }: AudioProviderProps) {
  const [state, dispatch] = useReducer(audioReducer, initialState);
  
  // Ref to track monitoring state for event handlers
  const monitoringRef = useRef(false);

  // Computed state for proceed logic
  const canProceedToNext = Boolean(
    (state.useDefaultInputDevice || state.selectedInputDevice) &&
    (state.useDefaultOutputDevice || state.selectedOutputDevice)
  );
  const isAudioReady = canProceedToNext;

  // ============================================================================
  // CORE AUDIO MANAGEMENT
  // ============================================================================

  // Internal function for loading devices (used by both loadDevices and refreshDevices)
  const loadDevicesInternal = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        dispatch({ type: 'SET_REFRESHING', payload: true });
      } else {
        dispatch({ type: 'SET_LOADING', payload: true });
      }
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.sendCommand) {
        throw new Error('Electron API not available');
      }

      const devicesResult = await window.electron.sendCommand('audio.get_devices');
      if (!devicesResult.success) {
        throw new Error(devicesResult.error || 'Failed to load audio devices');
      }

      const { devices } = devicesResult.data;
      if (!devices) throw new Error('No devices returned from backend');

      const mapDevice = (d: any, type: DeviceType): AudioDevice => ({
        id: d.id,
        name: d.name,
        type,
        isDefault: d.isDefault,
        isAvailable: true,
        channels: d.channels || (type === 'input' ? 1 : 2),
        sampleRate: d.sampleRate || 44100,
        hostApi: d.hostApi || 'unknown'
      });

      const inputDevices = devices.filter((d: any) => d.type === 'input').map((d: any) => mapDevice(d, 'input' as DeviceType));
      const outputDevices = devices.filter((d: any) => d.type === 'output').map((d: any) => mapDevice(d, 'output' as DeviceType));

      dispatch({ type: 'SET_DEVICES', payload: { input: inputDevices, output: outputDevices } });

      // Update selected devices if they don't exist
      if (!inputDevices.some((d: any) => d.id === state.selectedInputDevice)) {
        const defaultInput = inputDevices.find((d: any) => d.isDefault) || inputDevices[0];
        if (defaultInput) dispatch({ type: 'SELECT_INPUT_DEVICE', payload: defaultInput.id });
      }

      if (!outputDevices.some((d: any) => d.id === state.selectedOutputDevice)) {
        const defaultOutput = outputDevices.find((d: any) => d.isDefault) || outputDevices[0];
        if (defaultOutput) dispatch({ type: 'SELECT_OUTPUT_DEVICE', payload: defaultOutput.id });
      }

    } catch (error: any) {
      console.error('❌ Error loading audio devices:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    } finally {
      if (isRefresh) {
        dispatch({ type: 'SET_REFRESHING', payload: false });
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
  }, [state.selectedInputDevice, state.selectedOutputDevice]);

  const loadDevices = useCallback(async () => {
    await loadDevicesInternal(false);
  }, [loadDevicesInternal]);

  const selectInputDevice = useCallback((deviceId: string) => {
    dispatch({ type: 'SELECT_INPUT_DEVICE', payload: deviceId });
  }, []);

  const selectOutputDevice = useCallback((deviceId: string) => {
    dispatch({ type: 'SELECT_OUTPUT_DEVICE', payload: deviceId });
  }, []);

  const setUseDefaultInput = useCallback((useDefault: boolean) => {
    const selectedDevice = useDefault 
      ? state.inputDevices.find(d => d.isDefault)?.id || ''
      : state.inputDevices.find(d => !d.isDefault)?.id || state.inputDevices.find(d => d.isDefault)?.id || '';
    
    dispatch({ 
      type: 'BATCH_UPDATE_USE_DEFAULT_INPUT', 
      payload: { useDefault, selectedDevice }
    });
  }, [state.inputDevices]);

  const setUseDefaultOutput = useCallback((useDefault: boolean) => {
    const selectedDevice = useDefault 
      ? state.outputDevices.find(d => d.isDefault)?.id || ''
      : state.outputDevices.find(d => !d.isDefault)?.id || state.outputDevices.find(d => d.isDefault)?.id || '';
    
    dispatch({ 
      type: 'BATCH_UPDATE_USE_DEFAULT_OUTPUT', 
      payload: { useDefault, selectedDevice }
    });
  }, [state.outputDevices]);

  const startMonitoring = useCallback(async () => {
    try {
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.sendCommand) {
        dispatch({ type: 'SET_ERROR', payload: 'Electron API not available' });
        return;
      }

      if (state.isMonitoring) {
        console.log('⚠️ Level monitoring already active, ignoring request');
        return;
      }

      console.log('🔊 Starting level monitoring...');

      const result = await window.electron.sendCommand('audio.start_level_monitoring', {
        // No additional parameters needed for start_level_monitoring
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to start level monitoring');
      }

      console.log('✓ Level monitoring started successfully');
      monitoringRef.current = true;
      dispatch({ type: 'SET_MONITORING', payload: true });
    } catch (error: any) {
      console.error('❌ Error starting level monitoring:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  }, [state.isMonitoring]);

  const stopMonitoring = useCallback(async () => {
    try {
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.sendCommand) {
        dispatch({ type: 'SET_ERROR', payload: 'Electron API not available' });
        return;
      }

      if (!state.isMonitoring) {
        console.log('⚠️ Level monitoring not active, ignoring request');
        return;
      }

      console.log('🔇 Stopping level monitoring...');

      await window.electron.sendCommand('audio.stop_level_monitoring');
      
      console.log('✓ Level monitoring stopped successfully');
      monitoringRef.current = false;
      dispatch({ type: 'SET_MONITORING', payload: false });
    } catch (error: any) {
      console.error('❌ Error stopping level monitoring:', error);
      // Don't update state on error to prevent UI from showing stopped when it might not be
    }
  }, [state.isMonitoring]);

  const refreshDevices = useCallback(async () => {
    console.log('🔄 Refreshing audio devices...');

    // If monitoring is active, stop it before refreshing devices
    const wasMonitoring = monitoringRef.current;
    if (wasMonitoring) {
      console.log('🔇 Temporarily stopping monitoring for device refresh');
      await stopMonitoring();
    }

    // Refresh the devices using the internal function with refresh flag
    await loadDevicesInternal(true);

    // Restart monitoring if it was active before
    if (wasMonitoring) {
      console.log('🎤 Restarting monitoring after device refresh');
      // Small delay to ensure backend has processed the device changes
      setTimeout(async () => {
        await startMonitoring();
      }, 300);
    }
  }, [loadDevicesInternal, stopMonitoring, startMonitoring]);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // ============================================================================
  // SETTINGS MANAGEMENT
  // ============================================================================

  const loadSettings = useCallback(async () => {
    try {
      if (!window.electron?.getSettings) return;
      const settings = await window.electron.getSettings();
      
      if (typeof settings.useDefaultInputDevice === 'boolean') {
        dispatch({ type: 'SET_USE_DEFAULT_INPUT', payload: settings.useDefaultInputDevice });
      }
      if (typeof settings.useDefaultOutputDevice === 'boolean') {
        dispatch({ type: 'SET_USE_DEFAULT_OUTPUT', payload: settings.useDefaultOutputDevice });
      }
      if (settings.selectedInputDevice && !settings.useDefaultInputDevice) {
        dispatch({ type: 'SELECT_INPUT_DEVICE', payload: settings.selectedInputDevice });
      }
      if (settings.selectedOutputDevice && !settings.useDefaultOutputDevice) {
        dispatch({ type: 'SELECT_OUTPUT_DEVICE', payload: settings.selectedOutputDevice });
      }
    } catch (error: any) {
      console.error('❌ Error loading audio settings:', error);
    }
  }, []);

  // Simplified settings save with debouncing
  const debouncedSaveSettings = useRef<NodeJS.Timeout | null>(null);
  const saveSettings = useCallback(() => {
    if (debouncedSaveSettings.current) clearTimeout(debouncedSaveSettings.current);
    debouncedSaveSettings.current = setTimeout(async () => {
      try {
        if (!window.electron?.updateSettings) return;
        await window.electron.updateSettings({
          useDefaultInputDevice: state.useDefaultInputDevice,
          useDefaultOutputDevice: state.useDefaultOutputDevice,
          selectedInputDevice: state.useDefaultInputDevice ? null : state.selectedInputDevice,
          selectedOutputDevice: state.useDefaultOutputDevice ? null : state.selectedOutputDevice
        });
      } catch (error: any) {
        console.error('❌ Error saving audio settings:', error);
      }
    }, 500);
  }, [state.useDefaultInputDevice, state.useDefaultOutputDevice, state.selectedInputDevice, state.selectedOutputDevice]);



  // ============================================================================
  // PYTHON EVENT HANDLERS
  // ============================================================================

  useEffect(() => {
    if (!window.electron?.onBackendMessage) return;
    
    return window.electron.onBackendMessage((message: any) => {
      if (message.type === 'event' && message.event === 'audio_levels' && message.data.levels) {
        dispatch({ type: 'SET_AUDIO_LEVEL', payload: { microphone: message.data.levels.microphone || 0 } });
        
        if (!monitoringRef.current) {
          monitoringRef.current = true;
          dispatch({ type: 'SET_MONITORING', payload: true });
        }
      }
    });
  }, []);

  // ============================================================================
  // INITIALIZATION AND EFFECTS
  // ============================================================================

  const isInitialized = useRef(false);
  useEffect(() => {
    if (!isInitialized.current) {
      isInitialized.current = true;
      loadSettings().then(() => loadDevices());
    }
  }, [loadSettings, loadDevices]);

  useEffect(() => {
    if (isInitialized.current) saveSettings();
  }, [saveSettings]);

  // ============================================================================
  // STEP LIFECYCLE INTEGRATION
  // ============================================================================

  // Use the step integration hook to handle proceed state and lifecycle
  useStepIntegration({
    step: 'audio',
    canProceed: canProceedToNext,
    onEnter: async () => {
      console.log('🎵 Entering audio step - loading devices and starting monitoring');
      await loadDevices();
      
      // Always start monitoring when entering audio step
      // The Python backend will hot-swap to correct devices automatically
      if (!state.isMonitoring) {
        console.log('🎤 Starting audio level monitoring for audio step...');
        await startMonitoring();
      }
    },
    onExit: async () => {
      console.log('🎵 Exiting audio step - stopping monitoring');
      await stopMonitoring();
    },
    deps: [state.isMonitoring]
  });

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const contextValue: AudioContextValue = {
    state,
    loadDevices,
    selectInputDevice,
    selectOutputDevice,
    setUseDefaultInput,
    setUseDefaultOutput,
    startMonitoring,
    stopMonitoring,
    refreshDevices,
    clearError,
    canProceedToNext,
    isAudioReady,
  };

  // Cleanup debounced timeout on unmount
  useEffect(() => {
    return () => {
      if (debouncedSaveSettings.current) {
        clearTimeout(debouncedSaveSettings.current);
      }
    };
  }, []);

  return (
    <AudioContext.Provider value={contextValue}>
      {children}
    </AudioContext.Provider>
  );
}

// ============================================================================
// HOOK
// ============================================================================

export function useAudio(): AudioContextValue {
  const context = useContext(AudioContext);
  if (!context) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
} 